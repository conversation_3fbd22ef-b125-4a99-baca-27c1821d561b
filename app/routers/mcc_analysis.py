from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from sqlmodel import Session, select
import json
import traceback
import logging

from app.database import get_session
from app.models.db_models import MccAnalysis, WebsiteUrls, ScrapeRequestTracker
from app.models.request_models import MccAnalysisRequest
from app.tasks.celery_tasks import process_mcc_analysis
from app.utils.website_url_processor import store_urls_from_request, get_urls_by_scrape_ref

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/")
def create_or_get_mcc_analysis(
    request: MccAnalysisRequest, session: Session = Depends(get_session)
):
    """
    Create a new MCC analysis task or get existing one with comprehensive error handling
    
    This endpoint:
    1. Checks if an analysis with the same scrape request ID exists
    2. Creates a new MCC analysis record if needed
    3. Stores website URLs in the database
    4. Triggers a Celery task for asynchronous processing
    """
    try:
        # Validate request
        if not request or not hasattr(request, 'scrapeRequestRefID') or not request.scrapeRequestRefID:
            logger.error("Invalid request: missing scrapeRequestRefID")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: scrapeRequestRefID is required"
            )
        
        if not hasattr(request, 'website') or not request.website:
            logger.error("Invalid request: missing website")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: website is required"
            )
        
        logger.info(f"Processing MCC analysis request for {request.website} with ref_id {request.scrapeRequestRefID}")

        

        # Check if an MccAnalysis with the same scrapeRequestRefID exists
        try:
            statement = select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == request.scrapeRequestRefID
            )
            existing_analysis = session.exec(statement).first()
            
            if existing_analysis:
                logger.info(f"MCC analysis already exists with ID {existing_analysis.id}")
                return {
                    "message": "MccAnalysis already exists.", 
                    "id": existing_analysis.id,
                    "status": getattr(existing_analysis, 'processing_status', 'UNKNOWN')
                }
        except Exception as db_error:
            logger.error(f"Database error checking existing analysis: {str(db_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error while checking existing analysis"
            )

        # Store URLs from the request first
        try:
            url_store_result = store_urls_from_request(request, session, auto_classify=False)
            if url_store_result != 1:  # 1 indicates success, -1 indicates failure
                logger.error("Failed to store URLs")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to store website URLs"
                )
        except Exception as url_store_error:
            logger.error(f"Error storing URLs: {str(url_store_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing URLs: {str(url_store_error)}"
            )
        
        if not request.parsed_urls[0].urls or len(request.parsed_urls[0].urls) == 0:
            logger.error("Invalid request: no URLs provided")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: at least one URL depth with URLs is required"
            )
        
        # Create a new MccAnalysis from the request (no website_id needed)
        try:
            new_analysis = MccAnalysis(
                website=request.website,
                scrape_request_ref_id=request.scrapeRequestRefID,
                org_id=getattr(request, 'org_id', None) or "default",
                processing_status="PENDING"
            )
            session.add(new_analysis)
            session.commit()
            session.refresh(new_analysis)  # Refresh to get the auto-generated ID
            
            logger.info(f"Created new MCC analysis with ID {new_analysis.id}")
            
        except Exception as create_error:
            session.rollback()
            logger.error(f"Error creating MCC analysis record: {str(create_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating MCC analysis record: {str(create_error)}"
            )
        
        # Trigger Celery task for processing
        try:
            task = process_mcc_analysis.delay(new_analysis.id)
            task_id = task.id if hasattr(task, 'id') else 'unknown'
            
            logger.info(f"Triggered Celery task {task_id} for analysis {new_analysis.id}")
            
        except Exception as task_error:
            logger.error(f"Error triggering Celery task: {str(task_error)}")
            # Don't fail the request if task creation fails - analysis record is created
            task_id = None

        return {
            "message": "MccAnalysis created successfully.", 
            "id": new_analysis.id,
            "status": "PENDING",
            "task_id": task_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in create_or_get_mcc_analysis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )



@router.get("/by-request/{scrape_request_ref_id}")
def get_mcc_analysis_by_request_id(
    scrape_request_ref_id: str, 
    session: Session = Depends(get_session)
):
    """
    Get MCC analysis by scrape request reference ID
    """
    try:
        analysis = session.exec(
            select(MccAnalysis).where(
                MccAnalysis.scrape_request_ref_id == scrape_request_ref_id
            )
        ).first()
        
        if not analysis:
            raise HTTPException(
                status_code=404, 
                detail=f"MccAnalysis not found for request ID: {scrape_request_ref_id}"
            )
        
        # Parse details JSON if available
        details = {}
        if hasattr(analysis, 'details') and analysis.details:
            try:
                details = json.loads(analysis.details)
            except json.JSONDecodeError:
                details = {"error": "Invalid JSON in details field"}
        
        # Get MCC code
        mcc_code = analysis.mcc_code if hasattr(analysis, 'mcc_code') else None
        
        # Prepare response with details
        response = {
            "id": analysis.id,
            "website": analysis.website,
            "scrape_request_ref_id": analysis.scrape_request_ref_id,
            "mcc_code": mcc_code,
            "business_description": analysis.business_description if hasattr(analysis, 'business_description') else "",
            "reasoning": analysis.reasoning if hasattr(analysis, 'reasoning') else "",
            "processing_status": analysis.processing_status if hasattr(analysis, 'processing_status') else "UNKNOWN",
            "created_at": analysis.created_at if hasattr(analysis, 'created_at') else None,
            "started_at": analysis.started_at if hasattr(analysis, 'started_at') else None,
            "completed_at": analysis.completed_at if hasattr(analysis, 'completed_at') else None
        }
        
        # Add details only if they exist
        if details:
            response["details"] = details
            
            # Extract flow indicators from details to top level for easier access
            if isinstance(details, dict):
                flow_indicators = [
                    "analysis_flow_type", "content_availability_status", 
                    "fallback_method_used", "text_extraction_used", "insufficient_data"
                ]
                for indicator in flow_indicators:
                    if indicator in details:
                        response[indicator] = details[indicator]
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting MCC analysis by request ID {scrape_request_ref_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting MCC analysis: {str(e)}"
        )



